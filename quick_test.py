#!/usr/bin/env python3
"""
Quick test to check if we can connect to Sniper5_dx12.exe
"""

import pymem
import pymem.exception

def test_connection():
    process_name = "Sniper5_dx12.exe"
    print(f"Testing connection to {process_name}...")
    
    try:
        pm = pymem.Pymem(process_name)
        print(f"✅ SUCCESS! Connected to {process_name}")
        print(f"   Process ID: {pm.process_id}")
        print(f"   Base Address: 0x{pm.base_address:X}")
        pm.close_process()
        return True
    except pymem.exception.ProcessNotFound:
        print(f"❌ Process '{process_name}' not found")
        print("   Make sure Sniper Elite 5 is running and you're in a level/mission")
        return False
    except pymem.exception.CouldNotOpenProcess as e:
        print(f"❌ Could not open process '{process_name}': {e}")
        print("   This usually means insufficient privileges")
        print("   Try running as administrator")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("Sniper Elite 5 Connection Test")
    print("=" * 40)
    
    # Check admin privileges
    try:
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        print(f"Running as administrator: {'Yes' if is_admin else 'No'}")
    except:
        print("Could not check admin privileges")
    
    print()
    success = test_connection()
    
    if success:
        print("\n🎉 Connection successful! The main program should work now.")
    else:
        print("\n❌ Connection failed. Try:")
        print("1. Running as administrator")
        print("2. Making sure you're in a game level (not just main menu)")
        print("3. Checking if the game has anti-cheat protection")
    
    input("\nPress Enter to exit...")
