import time
import threading
from memory_manager import MemoryManager
from config import GameOffsets

class UnlimitedAmmo:
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
        self.enabled = False
        self.thread = None
        self.running = False
        
        # Store original values for restoration
        self.original_ammo = None
        self.original_reload_state = None
    
    def toggle(self):
        """Toggle unlimited ammo on/off"""
        if self.enabled:
            self.disable()
        else:
            self.enable()
    
    def enable(self):
        """Enable unlimited ammo"""
        if not self.memory_manager.process_attached:
            print("Error: Not attached to game process")
            return False
        
        if self.enabled:
            print("Unlimited ammo is already enabled")
            return True
        
        # Store original values
        self._store_original_values()
        
        self.enabled = True
        self.running = True
        self.thread = threading.Thread(target=self._unlimited_ammo_loop, daemon=True)
        self.thread.start()
        
        print("Unlimited ammo enabled! Press F4 to toggle.")
        return True
    
    def disable(self):
        """Disable unlimited ammo and restore original values"""
        if not self.enabled:
            print("Unlimited ammo is already disabled")
            return
        
        self.enabled = False
        self.running = False
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=1.0)
        
        # Restore original values
        self._restore_original_values()
        
        print("Unlimited ammo disabled")
    
    def _store_original_values(self):
        """Store original ammo and reload state values"""
        if not self.memory_manager.weapon_base:
            return
        
        ammo_addr = self.memory_manager.weapon_base + GameOffsets.AMMO_OFFSET
        reload_addr = self.memory_manager.weapon_base + GameOffsets.RELOAD_STATE_OFFSET
        
        self.original_ammo = self.memory_manager.read_int_safe(ammo_addr)
        self.original_reload_state = self.memory_manager.read_int_safe(reload_addr)
    
    def _restore_original_values(self):
        """Restore original ammo and reload state values"""
        if not self.memory_manager.weapon_base:
            return
        
        ammo_addr = self.memory_manager.weapon_base + GameOffsets.AMMO_OFFSET
        reload_addr = self.memory_manager.weapon_base + GameOffsets.RELOAD_STATE_OFFSET
        
        if self.original_ammo is not None:
            self.memory_manager.write_int_safe(ammo_addr, self.original_ammo)
        
        if self.original_reload_state is not None:
            self.memory_manager.write_int_safe(reload_addr, self.original_reload_state)
    
    def _get_all_weapon_addresses(self):
        """Get addresses for all equipped weapons"""
        weapon_addresses = []
        
        try:
            # Primary weapon
            if self.memory_manager.weapon_base:
                weapon_addresses.append(self.memory_manager.weapon_base)
            
            # Secondary weapon (offset by weapon slot size, typically 0x100-0x200)
            secondary_weapon_addr = self.memory_manager.weapon_base + 0x100
            if self.memory_manager.is_valid_address(secondary_weapon_addr):
                weapon_addresses.append(secondary_weapon_addr)
            
            # Sidearm (another offset)
            sidearm_addr = self.memory_manager.weapon_base + 0x200
            if self.memory_manager.is_valid_address(sidearm_addr):
                weapon_addresses.append(sidearm_addr)
            
        except Exception as e:
            print(f"Error getting weapon addresses: {e}")
        
        return weapon_addresses
    
    def _modify_weapon_ammo(self, weapon_base_addr):
        """Modify ammo for a specific weapon"""
        try:
            ammo_addr = weapon_base_addr + GameOffsets.AMMO_OFFSET
            reload_addr = weapon_base_addr + GameOffsets.RELOAD_STATE_OFFSET
            
            # Set ammo to maximum value
            current_ammo = self.memory_manager.read_int_safe(ammo_addr)
            if current_ammo is not None and current_ammo < GameOffsets.MAX_AMMO_VALUE:
                self.memory_manager.write_int_safe(ammo_addr, GameOffsets.MAX_AMMO_VALUE)
            
            # Prevent reload state (set to 0 = not reloading)
            current_reload_state = self.memory_manager.read_int_safe(reload_addr)
            if current_reload_state is not None and current_reload_state != 0:
                self.memory_manager.write_int_safe(reload_addr, 0)
            
        except Exception as e:
            print(f"Error modifying weapon ammo at {hex(weapon_base_addr)}: {e}")
    
    def _unlimited_ammo_loop(self):
        """Main unlimited ammo loop"""
        while self.running and self.enabled:
            try:
                # Update base addresses
                if not self.memory_manager.update_base_addresses():
                    time.sleep(GameOffsets.UPDATE_INTERVAL)
                    continue
                
                # Get all weapon addresses
                weapon_addresses = self._get_all_weapon_addresses()
                
                # Modify ammo for each weapon
                for weapon_addr in weapon_addresses:
                    self._modify_weapon_ammo(weapon_addr)
                
                time.sleep(GameOffsets.UPDATE_INTERVAL)
                
            except Exception as e:
                print(f"Error in unlimited ammo loop: {e}")
                time.sleep(GameOffsets.UPDATE_INTERVAL)
    
    def set_specific_ammo_count(self, ammo_count):
        """Set a specific ammo count instead of maximum"""
        if not self.enabled:
            print("Unlimited ammo must be enabled first")
            return
        
        if not self.memory_manager.weapon_base:
            print("No weapon base address available")
            return
        
        try:
            weapon_addresses = self._get_all_weapon_addresses()
            
            for weapon_addr in weapon_addresses:
                ammo_addr = weapon_addr + GameOffsets.AMMO_OFFSET
                self.memory_manager.write_int_safe(ammo_addr, ammo_count)
            
            print(f"Set ammo count to {ammo_count}")
            
        except Exception as e:
            print(f"Error setting specific ammo count: {e}")
    
    def get_current_ammo(self):
        """Get current ammo count for primary weapon"""
        if not self.memory_manager.weapon_base:
            return None
        
        try:
            ammo_addr = self.memory_manager.weapon_base + GameOffsets.AMMO_OFFSET
            return self.memory_manager.read_int_safe(ammo_addr)
        except Exception as e:
            print(f"Error getting current ammo: {e}")
            return None
