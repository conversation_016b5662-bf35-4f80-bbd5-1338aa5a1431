import time
import threading
import win32gui
import win32con
import win32api
from memory_manager import Memory<PERSON>anager
from config import GameOffsets

class ESP:
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
        self.enabled = False
        self.thread = None
        self.running = False
        self.game_hwnd = None
        
        # ESP drawing settings
        self.box_color = win32api.RGB(255, 0, 0)  # Red color
        self.box_size = 50
    
    def toggle(self):
        """Toggle ESP on/off"""
        if self.enabled:
            self.disable()
        else:
            self.enable()
    
    def enable(self):
        """Enable ESP"""
        if not self.memory_manager.process_attached:
            print("Error: Not attached to game process")
            return False
        
        if self.enabled:
            print("ESP is already enabled")
            return True
        
        # Find game window
        self.game_hwnd = win32gui.FindWindow(None, "Sniper Elite 5")
        if not self.game_hwnd:
            print("Error: Could not find Sniper Elite 5 window")
            return False
        
        self.enabled = True
        self.running = True
        self.thread = threading.Thread(target=self._esp_loop, daemon=True)
        self.thread.start()
        
        print("ESP enabled! Press F3 to toggle.")
        return True
    
    def disable(self):
        """Disable ESP"""
        if not self.enabled:
            print("ESP is already disabled")
            return
        
        self.enabled = False
        self.running = False
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=1.0)
        
        print("ESP disabled")
    
    def _get_enemy_positions(self):
        """Get positions of all enemies"""
        if not self.memory_manager.player_base:
            return []
        
        enemies = []
        try:
            enemy_list_addr = self.memory_manager.player_base + GameOffsets.ENEMY_LIST_OFFSET
            enemy_list_ptr = self.memory_manager.read_int_safe(enemy_list_addr)
            
            if not enemy_list_ptr:
                return enemies
            
            enemy_count = self.memory_manager.read_int_safe(enemy_list_ptr)
            if not enemy_count or enemy_count <= 0 or enemy_count > 100:  # Sanity check
                return enemies
            
            for i in range(min(enemy_count, 50)):  # Limit to 50 enemies max
                enemy_ptr_addr = enemy_list_ptr + 0x4 + (i * 0x8)
                enemy_ptr = self.memory_manager.read_int_safe(enemy_ptr_addr)
                
                if not enemy_ptr:
                    continue
                
                # Read enemy position
                x = self.memory_manager.read_float_safe(enemy_ptr + GameOffsets.ENEMY_POSITION_X_OFFSET)
                y = self.memory_manager.read_float_safe(enemy_ptr + GameOffsets.ENEMY_POSITION_Y_OFFSET)
                z = self.memory_manager.read_float_safe(enemy_ptr + GameOffsets.ENEMY_POSITION_Z_OFFSET)
                
                if x is not None and y is not None and z is not None:
                    enemies.append((x, y, z))
            
        except Exception as e:
            print(f"Error getting enemy positions: {e}")
        
        return enemies
    
    def _world_to_screen(self, world_pos):
        """Convert 3D world coordinates to 2D screen coordinates"""
        # This is a simplified conversion - in reality, you'd need the game's view matrix
        # For demonstration purposes, we'll use a basic projection
        try:
            x, y, z = world_pos
            
            # Get window dimensions
            rect = win32gui.GetWindowRect(self.game_hwnd)
            window_width = rect[2] - rect[0]
            window_height = rect[3] - rect[1]
            
            # Simple perspective projection (this needs to be adjusted based on game's camera)
            if z <= 0:  # Behind camera
                return None
            
            # Basic projection formula (needs game-specific view matrix for accuracy)
            fov = 90.0  # Field of view in degrees
            aspect_ratio = window_width / window_height
            
            screen_x = int((x / z) * (window_width / 2) + (window_width / 2))
            screen_y = int((y / z) * (window_height / 2) + (window_height / 2))
            
            # Check if coordinates are within screen bounds
            if 0 <= screen_x <= window_width and 0 <= screen_y <= window_height:
                return (screen_x, screen_y)
            
        except Exception as e:
            print(f"Error converting world to screen coordinates: {e}")
        
        return None
    
    def _draw_esp_boxes(self, enemies):
        """Draw ESP boxes for enemies"""
        if not self.game_hwnd or not enemies:
            return
        
        try:
            # Get device context for the game window
            dc = win32gui.GetDC(self.game_hwnd)
            if not dc:
                return
            
            # Create a red pen for drawing
            pen = win32gui.CreatePen(win32con.PS_SOLID, 2, self.box_color)
            old_pen = win32gui.SelectObject(dc, pen)
            
            # Set transparent background
            win32gui.SetBkMode(dc, win32con.TRANSPARENT)
            
            for enemy_pos in enemies:
                screen_pos = self._world_to_screen(enemy_pos)
                if screen_pos:
                    x, y = screen_pos
                    
                    # Draw a rectangle around the enemy
                    win32gui.Rectangle(dc, 
                                     x - self.box_size // 2, 
                                     y - self.box_size // 2,
                                     x + self.box_size // 2, 
                                     y + self.box_size // 2)
            
            # Clean up
            win32gui.SelectObject(dc, old_pen)
            win32gui.DeleteObject(pen)
            win32gui.ReleaseDC(self.game_hwnd, dc)
            
        except Exception as e:
            print(f"Error drawing ESP boxes: {e}")
    
    def _esp_loop(self):
        """Main ESP loop"""
        while self.running and self.enabled:
            try:
                # Update base addresses
                if not self.memory_manager.update_base_addresses():
                    time.sleep(GameOffsets.ESP_UPDATE_INTERVAL)
                    continue
                
                # Check if game window is still valid
                if not win32gui.IsWindow(self.game_hwnd):
                    print("Game window closed, disabling ESP")
                    self.disable()
                    break
                
                # Get enemy positions
                enemies = self._get_enemy_positions()
                
                # Draw ESP boxes
                if enemies:
                    self._draw_esp_boxes(enemies)
                
                time.sleep(GameOffsets.ESP_UPDATE_INTERVAL)
                
            except Exception as e:
                print(f"Error in ESP loop: {e}")
                time.sleep(GameOffsets.ESP_UPDATE_INTERVAL)
