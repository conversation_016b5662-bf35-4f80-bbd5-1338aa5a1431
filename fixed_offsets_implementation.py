#!/usr/bin/env python3
"""
Sniper Elite 5 - FIXED OFFSETS Implementation
==============================================

This implementation correctly handles RVAs (Relative Virtual Addresses)
from the memory analysis. The previous implementation was treating RVAs
as direct memory addresses, causing invalid negative addresses.

IMPORTANT: For offline/single-player use only!
"""

import sys
import time
import keyboard
import threading
from memory_manager import MemoryManager
from config import GameOffsets, HotkeyConfig

class FixedOffsetsSniper5Mods:
    def __init__(self):
        self.memory_manager = MemoryManager()
        self.running = True
        
        # Feature states
        self.unlimited_ammo_enabled = False
        
        # Threads
        self.unlimited_ammo_thread = None
        
        print("=" * 60)
        print("Sniper Elite 5 - FIXED OFFSETS Implementation")
        print("=" * 60)
        print("Properly handling RVAs from memory analysis!")
        print("IMPORTANT: For offline/single-player use only!")
        print("=" * 60)
    
    def initialize(self):
        """Initialize the modification system"""
        print("Attempting to attach to Sniper Elite 5...")
        
        if not self.memory_manager.attach_to_process():
            print("Failed to attach to game process.")
            return False
        
        print("Successfully attached to game process!")
        print(f"Base Address: 0x{self.memory_manager.pm.base_address:X}")
        
        # Test the RVA calculation
        self._test_rva_calculation()
        
        return True
    
    def _test_rva_calculation(self):
        """Test RVA calculation to see if addresses are valid"""
        print("\n" + "=" * 40)
        print("TESTING RVA CALCULATIONS")
        print("=" * 40)
        
        base_addr = self.memory_manager.pm.base_address
        
        # Test the weapon RVA
        weapon_rva_addr = base_addr + GameOffsets.WEAPON_BASE_OFFSET
        print(f"Base Address: 0x{base_addr:X}")
        print(f"Weapon RVA Offset: 0x{GameOffsets.WEAPON_BASE_OFFSET:X}")
        print(f"Weapon RVA Address: 0x{weapon_rva_addr:X}")
        
        # Try to read from the RVA address directly
        try:
            direct_value = self.memory_manager.read_int_safe(weapon_rva_addr)
            print(f"Direct RVA Read: 0x{direct_value:X}" if direct_value else "Direct RVA Read: Failed")
        except:
            print("Direct RVA Read: Failed")
        
        # Test player RVA
        player_rva_addr = base_addr + GameOffsets.PLAYER_BASE_OFFSET
        print(f"Player RVA Address: 0x{player_rva_addr:X}")
        
        try:
            player_direct_value = self.memory_manager.read_int_safe(player_rva_addr)
            print(f"Direct Player RVA Read: 0x{player_direct_value:X}" if player_direct_value else "Direct Player RVA Read: Failed")
        except:
            print("Direct Player RVA Read: Failed")
        
        print("=" * 40)
    
    def setup_hotkeys(self):
        """Setup keyboard hotkeys"""
        try:
            keyboard.add_hotkey('f4', self.toggle_unlimited_ammo)
            keyboard.add_hotkey('f12', self.exit_program)
            
            print("\nHotkeys configured:")
            print("F4 - Toggle Unlimited Ammo (Fixed RVA Implementation)")
            print("F12 - Exit Program")
            print("\nPress F4 to test the fixed implementation!")
            
        except Exception as e:
            print(f"Error setting up hotkeys: {e}")
            return False
        
        return True
    
    def toggle_unlimited_ammo(self):
        """Toggle unlimited ammo using FIXED RVA handling"""
        if self.unlimited_ammo_enabled:
            self.disable_unlimited_ammo()
        else:
            self.enable_unlimited_ammo()
    
    def enable_unlimited_ammo(self):
        """Enable unlimited ammo with FIXED RVA handling"""
        if not self.memory_manager.process_attached:
            print("Error: Not attached to game process")
            return
        
        self.unlimited_ammo_enabled = True
        self.unlimited_ammo_thread = threading.Thread(target=self._unlimited_ammo_loop_fixed, daemon=True)
        self.unlimited_ammo_thread.start()
        
        print("✅ Unlimited Ammo ENABLED (Using FIXED RVA handling)")
    
    def disable_unlimited_ammo(self):
        """Disable unlimited ammo"""
        self.unlimited_ammo_enabled = False
        print("❌ Unlimited Ammo DISABLED")
    
    def _unlimited_ammo_loop_fixed(self):
        """Fixed unlimited ammo loop that properly handles RVAs"""
        print("Starting FIXED unlimited ammo loop...")
        
        while self.unlimited_ammo_enabled:
            try:
                base_addr = self.memory_manager.pm.base_address
                
                # Method 1: Try direct RVA access (what we were doing wrong before)
                weapon_rva_addr = base_addr + GameOffsets.WEAPON_BASE_OFFSET
                
                # Method 2: Try reading the RVA as a pointer to the actual weapon data
                weapon_ptr = self.memory_manager.read_int_safe(weapon_rva_addr)
                
                if weapon_ptr and weapon_ptr > 0:
                    print(f"Found weapon pointer: 0x{weapon_ptr:X}")
                    
                    # Now try to access ammo at the weapon pointer + ammo offset
                    ammo_addr = weapon_ptr + GameOffsets.CURRENT_AMMO_OFFSET
                    reload_addr = weapon_ptr + GameOffsets.RELOAD_STATE_OFFSET
                    
                    print(f"Trying ammo address: 0x{ammo_addr:X}")
                    print(f"Trying reload address: 0x{reload_addr:X}")
                    
                    # Try to read current ammo first
                    current_ammo = self.memory_manager.read_int_safe(ammo_addr)
                    if current_ammo is not None:
                        print(f"Current ammo: {current_ammo}")
                        
                        # Try to set unlimited ammo
                        if self.memory_manager.write_int_safe(ammo_addr, 9999):
                            print("✅ Successfully wrote ammo!")
                        else:
                            print("❌ Failed to write ammo")
                    else:
                        print("❌ Could not read current ammo")
                    
                    # Try to prevent reload
                    current_reload = self.memory_manager.read_int_safe(reload_addr)
                    if current_reload is not None:
                        print(f"Current reload state: {current_reload}")
                        
                        if self.memory_manager.write_int_safe(reload_addr, 0):
                            print("✅ Successfully wrote reload state!")
                        else:
                            print("❌ Failed to write reload state")
                    else:
                        print("❌ Could not read reload state")
                
                else:
                    print(f"❌ Invalid weapon pointer: 0x{weapon_ptr:X}" if weapon_ptr else "❌ Could not read weapon pointer")
                
                # Method 3: Try scanning for ammo values in memory
                print("Attempting memory scan for ammo values...")
                self._scan_for_ammo_values(base_addr)
                
                time.sleep(2.0)  # Slower loop for debugging
                
            except Exception as e:
                print(f"Error in fixed unlimited ammo loop: {e}")
                time.sleep(2.0)
    
    def _scan_for_ammo_values(self, base_addr):
        """Scan memory around the base address for potential ammo values"""
        try:
            # Scan in a range around the weapon RVA
            weapon_rva_addr = base_addr + GameOffsets.WEAPON_BASE_OFFSET
            scan_start = weapon_rva_addr - 0x1000  # 4KB before
            scan_end = weapon_rva_addr + 0x1000    # 4KB after
            
            print(f"Scanning memory from 0x{scan_start:X} to 0x{scan_end:X}")
            
            found_values = []
            for addr in range(scan_start, scan_end, 4):  # Check every 4 bytes (int size)
                try:
                    value = self.memory_manager.read_int_safe(addr)
                    if value and 0 < value < 1000:  # Potential ammo values
                        found_values.append((addr, value))
                        if len(found_values) >= 10:  # Limit output
                            break
                except:
                    continue
            
            if found_values:
                print("Found potential ammo values:")
                for addr, value in found_values:
                    print(f"  0x{addr:X}: {value}")
            else:
                print("No potential ammo values found in scan range")
                
        except Exception as e:
            print(f"Error in memory scan: {e}")
    
    def exit_program(self):
        """Exit the program"""
        print("\nExiting program...")
        self.running = False
    
    def cleanup(self):
        """Clean up resources"""
        print("Cleaning up...")
        
        # Disable all features
        self.unlimited_ammo_enabled = False
        
        # Clean up memory manager
        if self.memory_manager:
            self.memory_manager.cleanup()
        
        print("Cleanup complete.")
    
    def run(self):
        """Main program loop"""
        if not self.initialize():
            return False
        
        if not self.setup_hotkeys():
            return False
        
        try:
            print("\n🔧 FIXED Implementation Running!")
            print("This version properly handles RVAs and includes debugging.")
            print("Press F4 to test unlimited ammo with detailed output.")
            print("Press F12 to exit.\n")
            
            # Main loop
            while self.running:
                time.sleep(0.1)
        
        except KeyboardInterrupt:
            print("\nReceived keyboard interrupt. Exiting...")
        
        except Exception as e:
            print(f"Unexpected error: {e}")
        
        finally:
            self.cleanup()
        
        return True

def main():
    """Main entry point"""
    print("Starting Sniper Elite 5 with FIXED OFFSETS...")
    
    # Check for administrator privileges
    try:
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            print("WARNING: Not running as administrator!")
            print("Some features may not work properly.")
            input("Press Enter to continue anyway, or Ctrl+C to exit...")
    except:
        pass
    
    # Create and run the modification system
    mods = FixedOffsetsSniper5Mods()
    success = mods.run()
    
    if success:
        print("Program exited successfully.")
    else:
        print("Program exited with errors.")
    
    input("Press Enter to close...")

if __name__ == "__main__":
    main()
