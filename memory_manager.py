import pymem
import pymem.exception
import time
import logging
from config import GameOffsets

class MemoryManager:
    def __init__(self):
        self.pm = None
        self.process_attached = False
        self.player_base = None
        self.weapon_base = None
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def attach_to_process(self):
        """Attach to the Sniper Elite 5 process"""
        try:
            self.pm = pymem.Pymem(GameOffsets.PROCESS_NAME)
            self.process_attached = True
            self.logger.info(f"Successfully attached to {GameOffsets.PROCESS_NAME}")
            return True
        except pymem.exception.ProcessNotFound:
            self.logger.error(f"Process {GameOffsets.PROCESS_NAME} not found. Make sure the game is running.")
            return False
        except Exception as e:
            self.logger.error(f"Failed to attach to process: {e}")
            return False
    
    def update_base_addresses(self):
        """Update player and weapon base addresses"""
        if not self.process_attached:
            return False
        
        try:
            self.player_base = self.pm.read_int(self.pm.base_address + GameOffsets.PLAYER_BASE_OFFSET)
            self.weapon_base = self.pm.read_int(self.pm.base_address + GameOffsets.WEAPON_BASE_OFFSET)
            return True
        except Exception as e:
            self.logger.error(f"Failed to update base addresses: {e}")
            return False
    
    def read_float_safe(self, address):
        """Safely read a float value from memory"""
        try:
            return self.pm.read_float(address)
        except Exception as e:
            self.logger.error(f"Failed to read float at address {hex(address)}: {e}")
            return None
    
    def write_float_safe(self, address, value):
        """Safely write a float value to memory"""
        try:
            self.pm.write_float(address, value)
            return True
        except Exception as e:
            self.logger.error(f"Failed to write float {value} at address {hex(address)}: {e}")
            return False
    
    def read_int_safe(self, address):
        """Safely read an integer value from memory"""
        try:
            return self.pm.read_int(address)
        except Exception as e:
            self.logger.error(f"Failed to read int at address {hex(address)}: {e}")
            return None
    
    def write_int_safe(self, address, value):
        """Safely write an integer value to memory"""
        try:
            self.pm.write_int(address, value)
            return True
        except Exception as e:
            self.logger.error(f"Failed to write int {value} at address {hex(address)}: {e}")
            return False
    
    def is_valid_address(self, address):
        """Check if an address is valid"""
        try:
            self.pm.read_bytes(address, 1)
            return True
        except:
            return False
    
    def cleanup(self):
        """Clean up resources"""
        if self.pm:
            try:
                self.pm.close_process()
            except:
                pass
        self.process_attached = False
