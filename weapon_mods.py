import time
import threading
from memory_manager import MemoryManager
from config import GameOffsets

class WeaponMods:
    """
    Weapon Modifications based on working cheat table by ctl3d32
    Features confirmed working in Sniper Elite 5:
    - No spread, No recoil, Bullet speed/damage multipliers, etc.
    """
    
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
        self.enabled = False
        self.thread = None
        self.running = False
        
        # Individual mod states
        self.no_spread_enabled = True
        self.no_recoil_enabled = True
        self.bullet_speed_enabled = True
        self.bullet_damage_enabled = True
        self.no_weapon_sound_enabled = False  # Can be annoying
        self.fire_rate_enabled = True
        self.no_camera_shake_enabled = True
        self.bullet_distance_enabled = True
        self.reload_speed_enabled = True
        self.bullet_gravity_enabled = True
        
        # Store original values for restoration
        self.original_values = {}
    
    def toggle(self):
        """Toggle weapon modifications on/off"""
        if self.enabled:
            self.disable()
        else:
            self.enable()
    
    def enable(self):
        """Enable weapon modifications"""
        if not self.memory_manager.process_attached:
            print("Error: Not attached to game process")
            return False
        
        if self.enabled:
            print("Weapon modifications are already enabled")
            return True
        
        # Store original values
        self._store_original_values()
        
        self.enabled = True
        self.running = True
        self.thread = threading.Thread(target=self._weapon_mods_loop, daemon=True)
        self.thread.start()
        
        print("Weapon modifications enabled! Press F5 to toggle.")
        print("Active mods: No Spread, No Recoil, Enhanced Damage, Fast Reload, etc.")
        return True
    
    def disable(self):
        """Disable weapon modifications and restore original values"""
        if not self.enabled:
            print("Weapon modifications are already disabled")
            return
        
        self.enabled = False
        self.running = False
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=1.0)
        
        # Restore original values
        self._restore_original_values()
        
        print("Weapon modifications disabled")
    
    def _store_original_values(self):
        """Store original weapon values"""
        if not self.memory_manager.weapon_base:
            return
        
        try:
            # Store original values for all weapon modifications
            offsets = [
                ('spread', GameOffsets.WEAPON_SPREAD_OFFSET),
                ('recoil', GameOffsets.WEAPON_RECOIL_OFFSET),
                ('bullet_speed', GameOffsets.WEAPON_BULLET_SPEED_OFFSET),
                ('damage', GameOffsets.WEAPON_DAMAGE_OFFSET),
                ('sound', GameOffsets.WEAPON_SOUND_OFFSET),
                ('fire_rate', GameOffsets.WEAPON_FIRE_RATE_OFFSET),
                ('camera_shake', GameOffsets.WEAPON_CAMERA_SHAKE_OFFSET),
                ('bullet_distance', GameOffsets.WEAPON_BULLET_DISTANCE_OFFSET),
                ('reload_speed', GameOffsets.WEAPON_RELOAD_SPEED_OFFSET),
                ('bullet_gravity', GameOffsets.WEAPON_BULLET_GRAVITY_OFFSET)
            ]
            
            for name, offset in offsets:
                addr = self.memory_manager.weapon_base + offset
                value = self.memory_manager.read_float_safe(addr)
                if value is not None:
                    self.original_values[name] = value
                    
        except Exception as e:
            print(f"Error storing original weapon values: {e}")
    
    def _restore_original_values(self):
        """Restore original weapon values"""
        if not self.memory_manager.weapon_base or not self.original_values:
            return
        
        try:
            offsets = [
                ('spread', GameOffsets.WEAPON_SPREAD_OFFSET),
                ('recoil', GameOffsets.WEAPON_RECOIL_OFFSET),
                ('bullet_speed', GameOffsets.WEAPON_BULLET_SPEED_OFFSET),
                ('damage', GameOffsets.WEAPON_DAMAGE_OFFSET),
                ('sound', GameOffsets.WEAPON_SOUND_OFFSET),
                ('fire_rate', GameOffsets.WEAPON_FIRE_RATE_OFFSET),
                ('camera_shake', GameOffsets.WEAPON_CAMERA_SHAKE_OFFSET),
                ('bullet_distance', GameOffsets.WEAPON_BULLET_DISTANCE_OFFSET),
                ('reload_speed', GameOffsets.WEAPON_RELOAD_SPEED_OFFSET),
                ('bullet_gravity', GameOffsets.WEAPON_BULLET_GRAVITY_OFFSET)
            ]
            
            for name, offset in offsets:
                if name in self.original_values:
                    addr = self.memory_manager.weapon_base + offset
                    self.memory_manager.write_float_safe(addr, self.original_values[name])
                    
        except Exception as e:
            print(f"Error restoring original weapon values: {e}")
    
    def _apply_weapon_modifications(self):
        """Apply weapon modifications based on enabled states"""
        if not self.memory_manager.weapon_base:
            return
        
        try:
            # No Spread
            if self.no_spread_enabled:
                addr = self.memory_manager.weapon_base + GameOffsets.WEAPON_SPREAD_OFFSET
                self.memory_manager.write_float_safe(addr, 0.0)
            
            # No Recoil
            if self.no_recoil_enabled:
                addr = self.memory_manager.weapon_base + GameOffsets.WEAPON_RECOIL_OFFSET
                self.memory_manager.write_float_safe(addr, 0.0)
            
            # Bullet Speed Multiplier
            if self.bullet_speed_enabled:
                addr = self.memory_manager.weapon_base + GameOffsets.WEAPON_BULLET_SPEED_OFFSET
                current_speed = self.memory_manager.read_float_safe(addr)
                if current_speed and current_speed > 0:
                    modified_speed = current_speed * GameOffsets.DEFAULT_BULLET_SPEED_MULTIPLIER
                    self.memory_manager.write_float_safe(addr, modified_speed)
            
            # Bullet Damage Multiplier
            if self.bullet_damage_enabled:
                addr = self.memory_manager.weapon_base + GameOffsets.WEAPON_DAMAGE_OFFSET
                current_damage = self.memory_manager.read_float_safe(addr)
                if current_damage and current_damage > 0:
                    modified_damage = current_damage * GameOffsets.DEFAULT_BULLET_DAMAGE_MULTIPLIER
                    self.memory_manager.write_float_safe(addr, modified_damage)
            
            # No Weapon Sound
            if self.no_weapon_sound_enabled:
                addr = self.memory_manager.weapon_base + GameOffsets.WEAPON_SOUND_OFFSET
                self.memory_manager.write_float_safe(addr, 0.0)
            
            # Fire Rate Multiplier
            if self.fire_rate_enabled:
                addr = self.memory_manager.weapon_base + GameOffsets.WEAPON_FIRE_RATE_OFFSET
                current_rate = self.memory_manager.read_float_safe(addr)
                if current_rate and current_rate > 0:
                    modified_rate = current_rate * GameOffsets.DEFAULT_FIRE_RATE_MULTIPLIER
                    self.memory_manager.write_float_safe(addr, modified_rate)
            
            # No Camera Shake
            if self.no_camera_shake_enabled:
                addr = self.memory_manager.weapon_base + GameOffsets.WEAPON_CAMERA_SHAKE_OFFSET
                self.memory_manager.write_float_safe(addr, 0.0)
            
            # Bullet Travel Distance Multiplier
            if self.bullet_distance_enabled:
                addr = self.memory_manager.weapon_base + GameOffsets.WEAPON_BULLET_DISTANCE_OFFSET
                current_distance = self.memory_manager.read_float_safe(addr)
                if current_distance and current_distance > 0:
                    modified_distance = current_distance * GameOffsets.DEFAULT_BULLET_DISTANCE_MULTIPLIER
                    self.memory_manager.write_float_safe(addr, modified_distance)
            
            # Reload Speed Multiplier
            if self.reload_speed_enabled:
                addr = self.memory_manager.weapon_base + GameOffsets.WEAPON_RELOAD_SPEED_OFFSET
                current_speed = self.memory_manager.read_float_safe(addr)
                if current_speed and current_speed > 0:
                    modified_speed = current_speed * GameOffsets.DEFAULT_RELOAD_SPEED_MULTIPLIER
                    self.memory_manager.write_float_safe(addr, modified_speed)
            
            # Bullet Gravity Multiplier
            if self.bullet_gravity_enabled:
                addr = self.memory_manager.weapon_base + GameOffsets.WEAPON_BULLET_GRAVITY_OFFSET
                current_gravity = self.memory_manager.read_float_safe(addr)
                if current_gravity and current_gravity > 0:
                    modified_gravity = current_gravity * GameOffsets.DEFAULT_BULLET_GRAVITY_MULTIPLIER
                    self.memory_manager.write_float_safe(addr, modified_gravity)
                    
        except Exception as e:
            print(f"Error applying weapon modifications: {e}")
    
    def _weapon_mods_loop(self):
        """Main weapon modifications loop"""
        while self.running and self.enabled:
            try:
                # Update base addresses
                if not self.memory_manager.update_base_addresses():
                    time.sleep(GameOffsets.UPDATE_INTERVAL)
                    continue
                
                # Apply weapon modifications
                self._apply_weapon_modifications()
                
                time.sleep(GameOffsets.UPDATE_INTERVAL)
                
            except Exception as e:
                print(f"Error in weapon mods loop: {e}")
                time.sleep(GameOffsets.UPDATE_INTERVAL)
    
    def toggle_individual_mod(self, mod_name):
        """Toggle individual weapon modifications"""
        mod_map = {
            'spread': 'no_spread_enabled',
            'recoil': 'no_recoil_enabled',
            'speed': 'bullet_speed_enabled',
            'damage': 'bullet_damage_enabled',
            'sound': 'no_weapon_sound_enabled',
            'fire_rate': 'fire_rate_enabled',
            'shake': 'no_camera_shake_enabled',
            'distance': 'bullet_distance_enabled',
            'reload': 'reload_speed_enabled',
            'gravity': 'bullet_gravity_enabled'
        }
        
        if mod_name in mod_map:
            attr_name = mod_map[mod_name]
            current_value = getattr(self, attr_name)
            setattr(self, attr_name, not current_value)
            print(f"{mod_name.title()} mod: {'Enabled' if not current_value else 'Disabled'}")
        else:
            print(f"Unknown mod: {mod_name}")
            print(f"Available mods: {', '.join(mod_map.keys())}")
    
    def get_status(self):
        """Get current status of all weapon modifications"""
        if not self.enabled:
            return "Weapon modifications: Disabled"
        
        status = "Weapon modifications: Enabled\n"
        mods = [
            ('No Spread', self.no_spread_enabled),
            ('No Recoil', self.no_recoil_enabled),
            ('Bullet Speed', self.bullet_speed_enabled),
            ('Bullet Damage', self.bullet_damage_enabled),
            ('No Weapon Sound', self.no_weapon_sound_enabled),
            ('Fire Rate', self.fire_rate_enabled),
            ('No Camera Shake', self.no_camera_shake_enabled),
            ('Bullet Distance', self.bullet_distance_enabled),
            ('Reload Speed', self.reload_speed_enabled),
            ('Bullet Gravity', self.bullet_gravity_enabled)
        ]
        
        for name, enabled in mods:
            status += f"  {name}: {'✓' if enabled else '✗'}\n"
        
        return status
