#!/usr/bin/env python3
"""
Sniper Elite 5 Offline Modifications
====================================

This tool provides various offline modifications for Sniper Elite 5:
- Super Jump (F1): Enhanced jumping abilities
- Aimbot (F2): Automatic target acquisition
- ESP (F3): Enemy position visualization
- Unlimited Ammo (F4): Infinite ammunition

IMPORTANT: This is for offline/single-player use only!

Usage:
1. Start Sniper Elite 5
2. Run this script as administrator
3. Use hotkeys to toggle features
4. Press F12 to exit

Author: Created for educational and offline gaming purposes
"""

import sys
import time
import keyboard
import threading
from memory_manager import MemoryManager
from super_jump import SuperJump
from aimbot import <PERSON><PERSON>t
from esp import ESP
from unlimited_ammo import UnlimitedAmmo
from weapon_mods import WeaponMods
from config import HotkeyConfig

class SniperElite5Mods:
    def __init__(self):
        self.memory_manager = MemoryManager()
        self.super_jump = None
        self.aimbot = None
        self.esp = None
        self.unlimited_ammo = None
        self.weapon_mods = None
        self.running = True
        
        print("=" * 60)
        print("Sniper Elite 5 Offline Modifications")
        print("=" * 60)
        print("IMPORTANT: For offline/single-player use only!")
        print("=" * 60)
    
    def initialize(self):
        """Initialize the modification system"""
        print("Attempting to attach to Sniper Elite 5...")
        
        if not self.memory_manager.attach_to_process():
            print("Failed to attach to game process.")
            print("Make sure:")
            print("1. Sniper Elite 5 is running")
            print("2. This script is run as administrator")
            print("3. The game process name matches 'SniperElite5.exe'")
            return False
        
        # Initialize modification modules
        self.super_jump = SuperJump(self.memory_manager)
        self.aimbot = Aimbot(self.memory_manager)
        self.esp = ESP(self.memory_manager)
        self.unlimited_ammo = UnlimitedAmmo(self.memory_manager)
        self.weapon_mods = WeaponMods(self.memory_manager)
        
        print("Successfully initialized!")
        return True
    
    def setup_hotkeys(self):
        """Setup keyboard hotkeys for toggling features"""
        try:
            keyboard.add_hotkey(HotkeyConfig.SUPER_JUMP_TOGGLE, self._toggle_super_jump)
            keyboard.add_hotkey(HotkeyConfig.AIMBOT_TOGGLE, self._toggle_aimbot)
            keyboard.add_hotkey(HotkeyConfig.ESP_TOGGLE, self._toggle_esp)
            keyboard.add_hotkey(HotkeyConfig.UNLIMITED_AMMO_TOGGLE, self._toggle_unlimited_ammo)
            keyboard.add_hotkey(HotkeyConfig.WEAPON_MODS_TOGGLE, self._toggle_weapon_mods)
            keyboard.add_hotkey(HotkeyConfig.EXIT_PROGRAM, self._exit_program)
            
            print("\nHotkeys configured:")
            print(f"F1 - Toggle Super Jump")
            print(f"F2 - Toggle Aimbot")
            print(f"F3 - Toggle ESP")
            print(f"F4 - Toggle Unlimited Ammo")
            print(f"F12 - Exit Program")
            print("\nPress the hotkeys to toggle features!")
            
        except Exception as e:
            print(f"Error setting up hotkeys: {e}")
            return False
        
        return True
    
    def _toggle_super_jump(self):
        """Toggle super jump feature"""
        if self.super_jump:
            self.super_jump.toggle()
    
    def _toggle_aimbot(self):
        """Toggle aimbot feature"""
        if self.aimbot:
            self.aimbot.toggle()
    
    def _toggle_esp(self):
        """Toggle ESP feature"""
        if self.esp:
            self.esp.toggle()
    
    def _toggle_unlimited_ammo(self):
        """Toggle unlimited ammo feature"""
        if self.unlimited_ammo:
            self.unlimited_ammo.toggle()
    
    def _exit_program(self):
        """Exit the program"""
        print("\nExiting program...")
        self.running = False
    
    def cleanup(self):
        """Clean up resources"""
        print("Cleaning up...")
        
        # Disable all features
        if self.super_jump and self.super_jump.enabled:
            self.super_jump.disable()
        
        if self.aimbot and self.aimbot.enabled:
            self.aimbot.disable()
        
        if self.esp and self.esp.enabled:
            self.esp.disable()
        
        if self.unlimited_ammo and self.unlimited_ammo.enabled:
            self.unlimited_ammo.disable()
        
        # Clean up memory manager
        if self.memory_manager:
            self.memory_manager.cleanup()
        
        print("Cleanup complete.")
    
    def run(self):
        """Main program loop"""
        if not self.initialize():
            return False
        
        if not self.setup_hotkeys():
            return False
        
        try:
            print("\nProgram is running. Use hotkeys to control features.")
            print("Press F12 to exit.\n")
            
            # Main loop
            while self.running:
                time.sleep(0.1)
                
                # Check if game process is still running
                if not self.memory_manager.process_attached:
                    print("Game process lost. Attempting to reconnect...")
                    if not self.memory_manager.attach_to_process():
                        print("Failed to reconnect. Exiting...")
                        break
        
        except KeyboardInterrupt:
            print("\nReceived keyboard interrupt. Exiting...")
        
        except Exception as e:
            print(f"Unexpected error: {e}")
        
        finally:
            self.cleanup()
        
        return True

def check_admin_privileges():
    """Check if the script is running with administrator privileges"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def main():
    """Main entry point"""
    print("Starting Sniper Elite 5 Offline Modifications...")
    
    # Check for administrator privileges
    if not check_admin_privileges():
        print("WARNING: This script should be run as administrator for best results.")
        print("Some features may not work properly without admin privileges.")
        input("Press Enter to continue anyway, or Ctrl+C to exit...")
    
    # Create and run the modification system
    mods = SniperElite5Mods()
    success = mods.run()
    
    if success:
        print("Program exited successfully.")
    else:
        print("Program exited with errors.")
    
    input("Press Enter to close...")

if __name__ == "__main__":
    main()
