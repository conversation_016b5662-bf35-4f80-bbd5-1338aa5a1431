# Sniper Elite 5 Memory Configuration
# Updated with more realistic patterns and AOB scanning approach

class GameOffsets:
    # Process name - Updated to target the actual game process, not the launcher
    PROCESS_NAME = "Sniper5_dx12.exe"

    # Alternative process names to try
    ALTERNATIVE_PROCESS_NAMES = [
        "Sniper5_dx12.exe",
        "Sniper5_vulkan.exe",
        "SniperElite5.exe"
    ]

    # AOB (Array of Bytes) patterns for more reliable scanning
    # These are example patterns - real ones would need to be found with Cheat Engine
    PLAYER_HEALTH_AOB = "48 8B 05 ?? ?? ?? ?? 48 85 C0 74 ?? F3 0F 10 80"
    AMMO_AOB = "89 91 ?? ?? ?? ?? 8B 81 ?? ?? ?? ?? 85 C0"
    GRAVITY_AOB = "F3 0F 11 83 ?? ?? ?? ?? F3 0F 10 83 ?? ?? ?? ??"

    # Fallback static offsets (these are realistic examples based on common patterns)
    # Base addresses - these would typically be found through pointer chains
    PLAYER_BASE_OFFSET = 0x4A2B8E0  # More realistic base offset
    WEAPON_BASE_OFFSET = 0x4A2C120

    # Player-related offsets (more realistic values)
    HEALTH_OFFSET = 0x140
    MAX_HEALTH_OFFSET = 0x144
    GRAVITY_OFFSET = 0x2A8
    JUMP_FORCE_OFFSET = 0x2AC
    POSITION_X_OFFSET = 0x80
    POSITION_Y_OFFSET = 0x84
    POSITION_Z_OFFSET = 0x88

    # Weapon-related offsets
    CURRENT_AMMO_OFFSET = 0x1B0
    MAX_AMMO_OFFSET = 0x1B4
    RELOAD_STATE_OFFSET = 0x1C8

    # Enemy-related offsets (these would be part of entity lists)
    ENTITY_LIST_OFFSET = 0x4A2D000
    ENTITY_SIZE = 0x200  # Size of each entity structure
    ENTITY_HEALTH_OFFSET = 0x140
    ENTITY_POSITION_X_OFFSET = 0x80
    ENTITY_POSITION_Y_OFFSET = 0x84
    ENTITY_POSITION_Z_OFFSET = 0x88
    
    # Modification values
    GRAVITY_MULTIPLIER = 0.1
    JUMP_FORCE_MULTIPLIER = 2.0
    MAX_AMMO_VALUE = 9999
    
    # Update intervals (in seconds)
    UPDATE_INTERVAL = 0.1
    ESP_UPDATE_INTERVAL = 0.05

class HotkeyConfig:
    # Hotkeys for toggling features
    SUPER_JUMP_TOGGLE = 'f1'
    AIMBOT_TOGGLE = 'f2'
    ESP_TOGGLE = 'f3'
    UNLIMITED_AMMO_TOGGLE = 'f4'
    EXIT_PROGRAM = 'f12'
