# Sniper Elite 5 Memory Offsets Configuration
# Note: These offsets may need to be updated for different game versions

class GameOffsets:
    # Process name - Updated to target the actual game process, not the launcher
    PROCESS_NAME = "Sniper5_dx12.exe"
    
    # Base offsets (these are examples and need verification)
    PLAYER_BASE_OFFSET = 0x15F4A80
    WEAPON_BASE_OFFSET = 0x16A4E80
    
    # Player-related offsets
    GRAVITY_OFFSET = 0x1A8
    JUMP_FORCE_OFFSET = 0x1AC
    AIM_ANGLE_OFFSET = 0x1C0
    
    # Weapon-related offsets
    AMMO_OFFSET = 0x2B0
    RELOAD_STATE_OFFSET = 0x2A8
    
    # Enemy-related offsets
    ENEMY_LIST_OFFSET = 0x1B0
    ENEMY_POSITION_X_OFFSET = 0x10
    ENEMY_POSITION_Y_OFFSET = 0x14
    ENEMY_POSITION_Z_OFFSET = 0x18
    
    # Modification values
    GRAVITY_MULTIPLIER = 0.1
    JUMP_FORCE_MULTIPLIER = 2.0
    MAX_AMMO_VALUE = 9999
    
    # Update intervals (in seconds)
    UPDATE_INTERVAL = 0.1
    ESP_UPDATE_INTERVAL = 0.05

class HotkeyConfig:
    # Hotkeys for toggling features
    SUPER_JUMP_TOGGLE = 'f1'
    AIMBOT_TOGGLE = 'f2'
    ESP_TOGGLE = 'f3'
    UNLIMITED_AMMO_TOGGLE = 'f4'
    EXIT_PROGRAM = 'f12'
