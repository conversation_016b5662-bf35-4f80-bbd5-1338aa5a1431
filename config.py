# Sniper Elite 5 Memory Configuration
# Updated with more realistic patterns and AOB scanning approach

class GameOffsets:
    # Process name - Updated to target the actual game process, not the launcher
    PROCESS_NAME = "Sniper5_dx12.exe"

    # Alternative process names to try
    ALTERNATIVE_PROCESS_NAMES = [
        "Sniper5_dx12.exe",
        "Sniper5_vulkan.exe",
        "SniperElite5.exe"
    ]

    # AOB (Array of Bytes) patterns for more reliable scanning
    # These are example patterns - real ones would need to be found with Cheat Engine
    PLAYER_HEALTH_AOB = "48 8B 05 ?? ?? ?? ?? 48 85 C0 74 ?? F3 0F 10 80"
    AMMO_AOB = "89 91 ?? ?? ?? ?? 8B 81 ?? ?? ?? ?? 85 C0"
    GRAVITY_AOB = "F3 0F 11 83 ?? ?? ?? ?? F3 0F 10 83 ?? ?? ?? ??"

    # REAL OFFSETS from actual Sniper Elite 5 memory analysis
    # Base addresses from offset analysis
    PLAYER_BASE_OFFSET = 0x5014EE    # Real player base from analysis
    WEAPON_BASE_OFFSET = 0x7942CD    # Real weapon base from analysis

    # Player-related offsets (from real memory analysis)
    HEALTH_OFFSET = 0x10           # Common entity field offset
    MAX_HEALTH_OFFSET = 0x14       # Common entity field offset
    GRAVITY_OFFSET = 0x1C          # From analysis - gravity within player structure
    JUMP_FORCE_OFFSET = 0x20       # From analysis - jump force within player structure
    POSITION_X_OFFSET = 0x10       # From analysis - enemy/entity X position
    POSITION_Y_OFFSET = 0x14       # From analysis - enemy/entity Y position
    POSITION_Z_OFFSET = 0x18       # From analysis - enemy/entity Z position
    AIM_ANGLE_OFFSET = 0x24        # From analysis - aim angles within player structure

    # Weapon-related offsets (from real memory analysis)
    CURRENT_AMMO_OFFSET = 0x2C     # From analysis - ammo within weapon structure
    MAX_AMMO_OFFSET = 0x30         # Adjacent to ammo offset
    RELOAD_STATE_OFFSET = 0x30     # From analysis - reload state within weapon structure

    # Enemy/Entity list offsets (from real memory analysis)
    ENEMY_LIST_OFFSET = 0x28       # From analysis - enemy list within player structure

    # Weapon modification offsets (based on struct field analysis)
    WEAPON_SPREAD_OFFSET = 0x40         # From struct field offsets
    WEAPON_RECOIL_OFFSET = 0x4C         # From struct field offsets
    WEAPON_BULLET_SPEED_OFFSET = 0x68   # From struct field offsets
    WEAPON_DAMAGE_OFFSET = 0x88         # From struct field offsets
    WEAPON_SOUND_OFFSET = 0xD8          # From struct field offsets
    WEAPON_FIRE_RATE_OFFSET = 0xD9      # From struct field offsets
    WEAPON_CAMERA_SHAKE_OFFSET = 0x228  # From struct field offsets
    WEAPON_BULLET_DISTANCE_OFFSET = 0x2C # From struct field offsets
    WEAPON_RELOAD_SPEED_OFFSET = 0x1C   # From struct field offsets
    WEAPON_BULLET_GRAVITY_OFFSET = 0x14 # From struct field offsets

    # Enemy-related offsets (from pointer path analysis)
    ENTITY_SIZE = 0x8    # Size of each entity pointer (from analysis: 0x4 + i * 0x8)
    ENTITY_HEALTH_OFFSET = 0x10     # From struct field offsets
    ENTITY_POSITION_X_OFFSET = 0x10 # From analysis - enemy X position
    ENTITY_POSITION_Y_OFFSET = 0x14 # From analysis - enemy Y position
    ENTITY_POSITION_Z_OFFSET = 0x18 # From analysis - enemy Z position

    # Additional real offsets from the analysis
    MODULE_RVA_OFFSETS = [
        0xF6278, 0xF6755, 0x5014EE, 0x5014FD, 0x7942CD,
        0x9B8F27, 0x9B8F6A, 0x9C09C0, 0x9F4202, 0xA58F6D
    ]
    
    # Modification values
    GRAVITY_MULTIPLIER = 0.1
    JUMP_FORCE_MULTIPLIER = 2.0
    MAX_AMMO_VALUE = 9999

    # Weapon modification default values (based on working cheat table)
    DEFAULT_BULLET_SPEED_MULTIPLIER = 2.0
    DEFAULT_BULLET_DAMAGE_MULTIPLIER = 2.0
    DEFAULT_FIRE_RATE_MULTIPLIER = 2.0
    DEFAULT_RELOAD_SPEED_MULTIPLIER = 2.0
    DEFAULT_BULLET_DISTANCE_MULTIPLIER = 2.0
    DEFAULT_BULLET_GRAVITY_MULTIPLIER = 0.5
    
    # Update intervals (in seconds)
    UPDATE_INTERVAL = 0.1
    ESP_UPDATE_INTERVAL = 0.05

class HotkeyConfig:
    # Hotkeys for toggling features
    SUPER_JUMP_TOGGLE = 'f1'
    AIMBOT_TOGGLE = 'f2'
    ESP_TOGGLE = 'f3'
    UNLIMITED_AMMO_TOGGLE = 'f4'
    WEAPON_MODS_TOGGLE = 'f5'  # New: Toggle weapon modifications
    EXIT_PROGRAM = 'f12'
