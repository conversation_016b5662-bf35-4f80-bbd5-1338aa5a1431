@echo off
echo Requesting administrator privileges...
echo.

REM Check if already running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Already running as administrator.
    goto :run_program
) else (
    echo Not running as administrator. Requesting elevation...
    powershell -Command "Start-Process cmd -ArgumentList '/c cd /d \"%~dp0\" && python main.py && pause' -Verb RunAs"
    goto :end
)

:run_program
echo Running Sniper Elite 5 modifications...
python main.py
pause

:end
