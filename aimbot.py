import time
import threading
import math
from memory_manager import MemoryManager
from config import GameOffsets

class Aimbot:
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
        self.enabled = False
        self.thread = None
        self.running = False
        self.max_aim_distance = 1000.0  # Maximum distance to aim at enemies
    
    def toggle(self):
        """Toggle aimbot on/off"""
        if self.enabled:
            self.disable()
        else:
            self.enable()
    
    def enable(self):
        """Enable aimbot"""
        if not self.memory_manager.process_attached:
            print("Error: Not attached to game process")
            return False
        
        if self.enabled:
            print("Aimbot is already enabled")
            return True
        
        self.enabled = True
        self.running = True
        self.thread = threading.Thread(target=self._aimbot_loop, daemon=True)
        self.thread.start()
        
        print("Aimbot enabled! Press F2 to toggle.")
        return True
    
    def disable(self):
        """Disable aimbot"""
        if not self.enabled:
            print("Aimbot is already disabled")
            return
        
        self.enabled = False
        self.running = False
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=1.0)
        
        print("Aimbo<PERSON> disabled")
    
    def _get_enemy_positions(self):
        """Get positions of all enemies"""
        if not self.memory_manager.player_base:
            return []
        
        enemies = []
        try:
            enemy_list_addr = self.memory_manager.player_base + GameOffsets.ENEMY_LIST_OFFSET
            enemy_list_ptr = self.memory_manager.read_int_safe(enemy_list_addr)
            
            if not enemy_list_ptr:
                return enemies
            
            enemy_count = self.memory_manager.read_int_safe(enemy_list_ptr)
            if not enemy_count or enemy_count <= 0 or enemy_count > 100:  # Sanity check
                return enemies
            
            for i in range(min(enemy_count, 50)):  # Limit to 50 enemies max
                enemy_ptr_addr = enemy_list_ptr + 0x4 + (i * 0x8)
                enemy_ptr = self.memory_manager.read_int_safe(enemy_ptr_addr)
                
                if not enemy_ptr:
                    continue
                
                # Read enemy position
                x = self.memory_manager.read_float_safe(enemy_ptr + GameOffsets.ENEMY_POSITION_X_OFFSET)
                y = self.memory_manager.read_float_safe(enemy_ptr + GameOffsets.ENEMY_POSITION_Y_OFFSET)
                z = self.memory_manager.read_float_safe(enemy_ptr + GameOffsets.ENEMY_POSITION_Z_OFFSET)
                
                if x is not None and y is not None and z is not None:
                    enemies.append((x, y, z))
            
        except Exception as e:
            print(f"Error getting enemy positions: {e}")
        
        return enemies
    
    def _calculate_distance(self, pos1, pos2):
        """Calculate 3D distance between two positions"""
        dx = pos1[0] - pos2[0]
        dy = pos1[1] - pos2[1]
        dz = pos1[2] - pos2[2]
        return math.sqrt(dx*dx + dy*dy + dz*dz)
    
    def _get_player_position(self):
        """Get player's current position"""
        if not self.memory_manager.player_base:
            return None
        
        try:
            x = self.memory_manager.read_float_safe(self.memory_manager.player_base + 0x10)
            y = self.memory_manager.read_float_safe(self.memory_manager.player_base + 0x14)
            z = self.memory_manager.read_float_safe(self.memory_manager.player_base + 0x18)
            
            if x is not None and y is not None and z is not None:
                return (x, y, z)
        except Exception as e:
            print(f"Error getting player position: {e}")
        
        return None
    
    def _find_closest_enemy(self, player_pos, enemies):
        """Find the closest enemy to the player"""
        if not enemies or not player_pos:
            return None
        
        closest_enemy = None
        closest_distance = float('inf')
        
        for enemy_pos in enemies:
            distance = self._calculate_distance(player_pos, enemy_pos)
            
            if distance < closest_distance and distance <= self.max_aim_distance:
                closest_distance = distance
                closest_enemy = enemy_pos
        
        return closest_enemy
    
    def _aim_at_target(self, target_pos):
        """Aim at the specified target position"""
        if not self.memory_manager.player_base or not target_pos:
            return
        
        try:
            aim_x_addr = self.memory_manager.player_base + GameOffsets.AIM_ANGLE_OFFSET
            aim_y_addr = self.memory_manager.player_base + GameOffsets.AIM_ANGLE_OFFSET + 0x4
            aim_z_addr = self.memory_manager.player_base + GameOffsets.AIM_ANGLE_OFFSET + 0x8
            
            # Write target coordinates to aim angles
            self.memory_manager.write_float_safe(aim_x_addr, target_pos[0])
            self.memory_manager.write_float_safe(aim_y_addr, target_pos[1])
            self.memory_manager.write_float_safe(aim_z_addr, target_pos[2])
            
        except Exception as e:
            print(f"Error aiming at target: {e}")
    
    def _aimbot_loop(self):
        """Main aimbot loop"""
        while self.running and self.enabled:
            try:
                # Update base addresses
                if not self.memory_manager.update_base_addresses():
                    time.sleep(GameOffsets.UPDATE_INTERVAL)
                    continue
                
                # Get player position
                player_pos = self._get_player_position()
                if not player_pos:
                    time.sleep(GameOffsets.UPDATE_INTERVAL)
                    continue
                
                # Get enemy positions
                enemies = self._get_enemy_positions()
                if not enemies:
                    time.sleep(GameOffsets.UPDATE_INTERVAL)
                    continue
                
                # Find closest enemy
                closest_enemy = self._find_closest_enemy(player_pos, enemies)
                if closest_enemy:
                    self._aim_at_target(closest_enemy)
                
                time.sleep(GameOffsets.UPDATE_INTERVAL)
                
            except Exception as e:
                print(f"Error in aimbot loop: {e}")
                time.sleep(GameOffsets.UPDATE_INTERVAL)
