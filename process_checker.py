#!/usr/bin/env python3
"""
Process Checker for Sniper Elite 5
Helps identify the correct process name and troubleshoot connection issues
"""

import psutil
import pymem
import sys

def list_sniper_elite_processes():
    """List all processes that might be related to Sniper Elite 5"""
    print("Searching for Sniper Elite 5 related processes...")
    print("=" * 60)
    
    sniper_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            proc_name = proc.info['name'].lower()
            if 'sniper' in proc_name or 'elite' in proc_name:
                sniper_processes.append(proc.info)
                print(f"Found: {proc.info['name']} (PID: {proc.info['pid']})")
                if proc.info['exe']:
                    print(f"  Path: {proc.info['exe']}")
                print()
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if not sniper_processes:
        print("No Sniper Elite processes found!")
        print("\nTrying broader search for game-related processes...")
        print("=" * 60)
        
        # Look for common game launcher processes
        game_keywords = ['steam', 'epic', 'uplay', 'origin', 'launcher']
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                proc_name = proc.info['name'].lower()
                for keyword in game_keywords:
                    if keyword in proc_name:
                        print(f"Game launcher: {proc.info['name']} (PID: {proc.info['pid']})")
                        break
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
    
    return sniper_processes

def test_process_connection(process_name):
    """Test if we can connect to a specific process"""
    print(f"\nTesting connection to '{process_name}'...")
    print("-" * 40)
    
    try:
        pm = pymem.Pymem(process_name)
        print(f"✅ Successfully connected to {process_name}")
        print(f"   Process ID: {pm.process_id}")
        print(f"   Base Address: 0x{pm.base_address:X}")
        pm.close_process()
        return True
    except pymem.exception.ProcessNotFound:
        print(f"❌ Process '{process_name}' not found")
        return False
    except pymem.exception.CouldNotOpenProcess as e:
        print(f"❌ Could not open process '{process_name}': {e}")
        print("   This usually means insufficient privileges (need admin rights)")
        return False
    except Exception as e:
        print(f"❌ Unexpected error connecting to '{process_name}': {e}")
        return False

def check_admin_privileges():
    """Check if running with administrator privileges"""
    try:
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        print(f"Administrator privileges: {'✅ Yes' if is_admin else '❌ No'}")
        return is_admin
    except:
        print("❌ Could not check administrator privileges")
        return False

def main():
    print("Sniper Elite 5 Process Diagnostic Tool")
    print("=" * 60)
    
    # Check admin privileges
    is_admin = check_admin_privileges()
    print()
    
    # List all Sniper Elite processes
    processes = list_sniper_elite_processes()
    
    # Test common process names
    common_names = [
        "SniperElite5.exe",
        "sniperelite5.exe",
        "SniperElite5_DX12.exe",
        "SniperElite5_Vulkan.exe",
        "SE5.exe",
        "Sniper Elite 5.exe",
        "Sniper5_dx12.exe",  # Added the actual game process we found
        "Sniper5_vulkan.exe"
    ]
    
    print("\nTesting common Sniper Elite 5 process names...")
    print("=" * 60)
    
    successful_connections = []
    for name in common_names:
        if test_process_connection(name):
            successful_connections.append(name)
    
    # Summary
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    if not is_admin:
        print("⚠️  WARNING: Not running as administrator!")
        print("   Run this script as administrator for best results.")
        print()
    
    if successful_connections:
        print("✅ Successfully connected to:")
        for name in successful_connections:
            print(f"   - {name}")
        print(f"\nUpdate config.py to use: PROCESS_NAME = \"{successful_connections[0]}\"")
    else:
        print("❌ Could not connect to any Sniper Elite 5 processes")
        if processes:
            print("\nFound these Sniper Elite processes but couldn't connect:")
            for proc in processes:
                print(f"   - {proc['name']} (PID: {proc['pid']})")
        else:
            print("\nNo Sniper Elite processes found. Make sure the game is running.")
    
    print("\nTroubleshooting tips:")
    print("1. Make sure Sniper Elite 5 is actually running")
    print("2. Run this script as administrator")
    print("3. Check if the game has anti-cheat protection")
    print("4. Try running the game in windowed mode")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
