#!/usr/bin/env python3
"""
Sniper Elite 5 Real Offsets Implementation
==========================================

This implementation uses REAL memory offsets extracted from actual 
Sniper Elite 5 memory analysis, not placeholder values.

Based on the offset analysis provided:
- Module RVAs: 0xF6278, 0xF6755, 0x5014EE, 0x5014FD, 0x7942CD, etc.
- Struct Field Offsets: 0x10, 0x14, 0x1C, 0x2C, 0x40, 0x4C, etc.
- Pointer Path Offsets: 0x0 to 0xFF8 range

IMPORTANT: For offline/single-player use only!
"""

import sys
import time
import keyboard
import threading
from memory_manager import MemoryManager
from config import GameOffsets, HotkeyConfig

class RealOffsetsSniper5Mods:
    def __init__(self):
        self.memory_manager = MemoryManager()
        self.running = True
        
        # Feature states
        self.super_jump_enabled = False
        self.aimbot_enabled = False
        self.unlimited_ammo_enabled = False
        self.weapon_mods_enabled = False
        
        # Threads
        self.super_jump_thread = None
        self.aimbot_thread = None
        self.unlimited_ammo_thread = None
        self.weapon_mods_thread = None
        
        # Original values storage
        self.original_values = {}
        
        print("=" * 60)
        print("Sniper Elite 5 - REAL OFFSETS Implementation")
        print("=" * 60)
        print("Based on actual memory analysis with real offsets!")
        print("IMPORTANT: For offline/single-player use only!")
        print("=" * 60)
    
    def initialize(self):
        """Initialize the modification system"""
        print("Attempting to attach to Sniper Elite 5...")
        
        if not self.memory_manager.attach_to_process():
            print("Failed to attach to game process.")
            print("Make sure:")
            print("1. Sniper Elite 5 is running and you're in a level")
            print("2. This script is run as administrator")
            print("3. The game process matches expected names")
            return False
        
        print("Successfully attached to game process!")
        return True
    
    def setup_hotkeys(self):
        """Setup keyboard hotkeys"""
        try:
            keyboard.add_hotkey('f1', self.toggle_super_jump)
            keyboard.add_hotkey('f2', self.toggle_aimbot)
            keyboard.add_hotkey('f4', self.toggle_unlimited_ammo)
            keyboard.add_hotkey('f5', self.toggle_weapon_mods)
            keyboard.add_hotkey('f12', self.exit_program)
            
            print("\nHotkeys configured:")
            print("F1 - Toggle Super Jump (Real Gravity/Jump Offsets)")
            print("F2 - Toggle Aimbot (Real Aim/Enemy Offsets)")
            print("F4 - Toggle Unlimited Ammo (Real Weapon Offsets)")
            print("F5 - Toggle Weapon Mods (Real Weapon Struct Offsets)")
            print("F12 - Exit Program")
            print("\nPress hotkeys to activate features!")
            
        except Exception as e:
            print(f"Error setting up hotkeys: {e}")
            return False
        
        return True
    
    def toggle_super_jump(self):
        """Toggle super jump using real offsets"""
        if self.super_jump_enabled:
            self.disable_super_jump()
        else:
            self.enable_super_jump()
    
    def enable_super_jump(self):
        """Enable super jump with real offsets"""
        if not self.memory_manager.process_attached:
            print("Error: Not attached to game process")
            return
        
        self.super_jump_enabled = True
        self.super_jump_thread = threading.Thread(target=self._super_jump_loop, daemon=True)
        self.super_jump_thread.start()
        
        print("✅ Super Jump ENABLED (Using real offsets: 0x5014EE + 0x1C/0x20)")
    
    def disable_super_jump(self):
        """Disable super jump"""
        self.super_jump_enabled = False
        if 'gravity' in self.original_values:
            # Restore original gravity
            self._restore_original_value('gravity', GameOffsets.PLAYER_BASE_OFFSET, GameOffsets.GRAVITY_OFFSET)
        if 'jump_force' in self.original_values:
            # Restore original jump force
            self._restore_original_value('jump_force', GameOffsets.PLAYER_BASE_OFFSET, GameOffsets.JUMP_FORCE_OFFSET)
        
        print("❌ Super Jump DISABLED")
    
    def toggle_unlimited_ammo(self):
        """Toggle unlimited ammo using real offsets"""
        if self.unlimited_ammo_enabled:
            self.disable_unlimited_ammo()
        else:
            self.enable_unlimited_ammo()
    
    def enable_unlimited_ammo(self):
        """Enable unlimited ammo with real offsets"""
        if not self.memory_manager.process_attached:
            print("Error: Not attached to game process")
            return
        
        self.unlimited_ammo_enabled = True
        self.unlimited_ammo_thread = threading.Thread(target=self._unlimited_ammo_loop, daemon=True)
        self.unlimited_ammo_thread.start()
        
        print("✅ Unlimited Ammo ENABLED (Using real offsets: 0x7942CD + 0x2C)")
    
    def disable_unlimited_ammo(self):
        """Disable unlimited ammo"""
        self.unlimited_ammo_enabled = False
        print("❌ Unlimited Ammo DISABLED")
    
    def toggle_aimbot(self):
        """Toggle aimbot using real offsets"""
        if self.aimbot_enabled:
            self.disable_aimbot()
        else:
            self.enable_aimbot()
    
    def enable_aimbot(self):
        """Enable aimbot with real offsets"""
        if not self.memory_manager.process_attached:
            print("Error: Not attached to game process")
            return
        
        self.aimbot_enabled = True
        self.aimbot_thread = threading.Thread(target=self._aimbot_loop, daemon=True)
        self.aimbot_thread.start()
        
        print("✅ Aimbot ENABLED (Using real offsets: 0x5014EE + 0x24/0x28)")
    
    def disable_aimbot(self):
        """Disable aimbot"""
        self.aimbot_enabled = False
        print("❌ Aimbot DISABLED")
    
    def toggle_weapon_mods(self):
        """Toggle weapon modifications using real struct offsets"""
        if self.weapon_mods_enabled:
            self.disable_weapon_mods()
        else:
            self.enable_weapon_mods()
    
    def enable_weapon_mods(self):
        """Enable weapon mods with real struct field offsets"""
        if not self.memory_manager.process_attached:
            print("Error: Not attached to game process")
            return
        
        self.weapon_mods_enabled = True
        self.weapon_mods_thread = threading.Thread(target=self._weapon_mods_loop, daemon=True)
        self.weapon_mods_thread.start()
        
        print("✅ Weapon Mods ENABLED (Using real struct offsets: 0x40, 0x4C, 0x68, etc.)")
    
    def disable_weapon_mods(self):
        """Disable weapon modifications"""
        self.weapon_mods_enabled = False
        print("❌ Weapon Mods DISABLED")
    
    def _super_jump_loop(self):
        """Super jump loop using REAL offsets from analysis"""
        while self.super_jump_enabled:
            try:
                if not self.memory_manager.update_base_addresses():
                    time.sleep(0.1)
                    continue
                
                # Use REAL OFFSET: 0x5014EE for player base
                player_base = self.memory_manager.read_int_safe(
                    self.memory_manager.pm.base_address + GameOffsets.PLAYER_BASE_OFFSET
                )
                
                if player_base:
                    # Use REAL OFFSET: 0x1C for gravity within player structure
                    gravity_addr = player_base + GameOffsets.GRAVITY_OFFSET
                    current_gravity = self.memory_manager.read_float_safe(gravity_addr)
                    
                    if current_gravity and current_gravity > 0:
                        # Store original if not stored
                        if 'gravity' not in self.original_values:
                            self.original_values['gravity'] = current_gravity
                        
                        # Apply super jump (reduce gravity)
                        modified_gravity = current_gravity * 0.1
                        self.memory_manager.write_float_safe(gravity_addr, modified_gravity)
                    
                    # Use REAL OFFSET: 0x20 for jump force within player structure
                    jump_addr = player_base + GameOffsets.JUMP_FORCE_OFFSET
                    current_jump = self.memory_manager.read_float_safe(jump_addr)
                    
                    if current_jump and current_jump > 0:
                        # Store original if not stored
                        if 'jump_force' not in self.original_values:
                            self.original_values['jump_force'] = current_jump
                        
                        # Apply super jump (increase jump force)
                        modified_jump = current_jump * 2.0
                        self.memory_manager.write_float_safe(jump_addr, modified_jump)
                
                time.sleep(0.1)
                
            except Exception as e:
                print(f"Error in super jump loop: {e}")
                time.sleep(0.1)
    
    def _unlimited_ammo_loop(self):
        """Unlimited ammo loop using REAL offsets from analysis"""
        while self.unlimited_ammo_enabled:
            try:
                if not self.memory_manager.update_base_addresses():
                    time.sleep(0.1)
                    continue
                
                # Use REAL OFFSET: 0x7942CD for weapon base
                weapon_base = self.memory_manager.read_int_safe(
                    self.memory_manager.pm.base_address + GameOffsets.WEAPON_BASE_OFFSET
                )
                
                if weapon_base:
                    # Use REAL OFFSET: 0x2C for ammo within weapon structure
                    ammo_addr = weapon_base + GameOffsets.CURRENT_AMMO_OFFSET
                    self.memory_manager.write_int_safe(ammo_addr, 9999)
                    
                    # Use REAL OFFSET: 0x30 for reload state within weapon structure
                    reload_addr = weapon_base + GameOffsets.RELOAD_STATE_OFFSET
                    self.memory_manager.write_int_safe(reload_addr, 0)
                
                time.sleep(0.1)
                
            except Exception as e:
                print(f"Error in unlimited ammo loop: {e}")
                time.sleep(0.1)
    
    def _aimbot_loop(self):
        """Aimbot loop using REAL offsets from analysis"""
        while self.aimbot_enabled:
            try:
                if not self.memory_manager.update_base_addresses():
                    time.sleep(0.1)
                    continue
                
                # Use REAL OFFSET: 0x5014EE for player base
                player_base = self.memory_manager.read_int_safe(
                    self.memory_manager.pm.base_address + GameOffsets.PLAYER_BASE_OFFSET
                )
                
                if player_base:
                    # Use REAL OFFSET: 0x28 for enemy list within player structure
                    enemy_list_addr = self.memory_manager.read_int_safe(player_base + GameOffsets.ENEMY_LIST_OFFSET)
                    
                    if enemy_list_addr:
                        enemy_count = self.memory_manager.read_int_safe(enemy_list_addr)
                        
                        if enemy_count and 0 < enemy_count < 50:  # Sanity check
                            closest_enemy = self._find_closest_enemy(enemy_list_addr, enemy_count)
                            
                            if closest_enemy:
                                # Use REAL OFFSET: 0x24 for aim angles within player structure
                                aim_addr = player_base + GameOffsets.AIM_ANGLE_OFFSET
                                self.memory_manager.write_float_safe(aim_addr, closest_enemy[0])
                                self.memory_manager.write_float_safe(aim_addr + 0x4, closest_enemy[1])
                                self.memory_manager.write_float_safe(aim_addr + 0x8, closest_enemy[2])
                
                time.sleep(0.1)
                
            except Exception as e:
                print(f"Error in aimbot loop: {e}")
                time.sleep(0.1)
    
    def _weapon_mods_loop(self):
        """Weapon modifications loop using REAL struct field offsets"""
        while self.weapon_mods_enabled:
            try:
                if not self.memory_manager.update_base_addresses():
                    time.sleep(0.1)
                    continue
                
                # Use REAL OFFSET: 0x7942CD for weapon base
                weapon_base = self.memory_manager.read_int_safe(
                    self.memory_manager.pm.base_address + GameOffsets.WEAPON_BASE_OFFSET
                )
                
                if weapon_base:
                    # Apply weapon modifications using REAL struct field offsets
                    # No spread (0x40 from struct field offsets)
                    self.memory_manager.write_float_safe(weapon_base + GameOffsets.WEAPON_SPREAD_OFFSET, 0.0)
                    
                    # No recoil (0x4C from struct field offsets)
                    self.memory_manager.write_float_safe(weapon_base + GameOffsets.WEAPON_RECOIL_OFFSET, 0.0)
                    
                    # Enhanced damage (0x88 from struct field offsets)
                    damage_addr = weapon_base + GameOffsets.WEAPON_DAMAGE_OFFSET
                    current_damage = self.memory_manager.read_float_safe(damage_addr)
                    if current_damage and current_damage > 0:
                        self.memory_manager.write_float_safe(damage_addr, current_damage * 2.0)
                
                time.sleep(0.1)
                
            except Exception as e:
                print(f"Error in weapon mods loop: {e}")
                time.sleep(0.1)
    
    def _find_closest_enemy(self, enemy_list_addr, enemy_count):
        """Find closest enemy using real offsets"""
        closest_enemy = None
        closest_distance = float('inf')
        
        try:
            for i in range(min(enemy_count, 20)):  # Limit to 20 enemies
                # Use REAL pattern: enemy_list_address + 0x4 + i * 0x8
                enemy_ptr_addr = enemy_list_addr + 0x4 + (i * GameOffsets.ENTITY_SIZE)
                enemy_ptr = self.memory_manager.read_int_safe(enemy_ptr_addr)
                
                if enemy_ptr:
                    # Use REAL OFFSETS: 0x10, 0x14, 0x18 for enemy positions
                    x = self.memory_manager.read_float_safe(enemy_ptr + GameOffsets.ENTITY_POSITION_X_OFFSET)
                    y = self.memory_manager.read_float_safe(enemy_ptr + GameOffsets.ENTITY_POSITION_Y_OFFSET)
                    z = self.memory_manager.read_float_safe(enemy_ptr + GameOffsets.ENTITY_POSITION_Z_OFFSET)
                    
                    if x is not None and y is not None and z is not None:
                        distance = (x*x + y*y + z*z) ** 0.5
                        if distance < closest_distance and distance > 1.0:  # Avoid self
                            closest_distance = distance
                            closest_enemy = (x, y, z)
        
        except Exception as e:
            print(f"Error finding closest enemy: {e}")
        
        return closest_enemy
    
    def _restore_original_value(self, key, base_offset, field_offset):
        """Restore original value"""
        if key in self.original_values:
            try:
                base_addr = self.memory_manager.read_int_safe(
                    self.memory_manager.pm.base_address + base_offset
                )
                if base_addr:
                    addr = base_addr + field_offset
                    self.memory_manager.write_float_safe(addr, self.original_values[key])
            except Exception as e:
                print(f"Error restoring {key}: {e}")
    
    def exit_program(self):
        """Exit the program"""
        print("\nExiting program...")
        self.running = False
    
    def cleanup(self):
        """Clean up resources"""
        print("Cleaning up...")
        
        # Disable all features
        self.super_jump_enabled = False
        self.aimbot_enabled = False
        self.unlimited_ammo_enabled = False
        self.weapon_mods_enabled = False
        
        # Restore original values
        for key in list(self.original_values.keys()):
            if key == 'gravity':
                self._restore_original_value(key, GameOffsets.PLAYER_BASE_OFFSET, GameOffsets.GRAVITY_OFFSET)
            elif key == 'jump_force':
                self._restore_original_value(key, GameOffsets.PLAYER_BASE_OFFSET, GameOffsets.JUMP_FORCE_OFFSET)
        
        # Clean up memory manager
        if self.memory_manager:
            self.memory_manager.cleanup()
        
        print("Cleanup complete.")
    
    def run(self):
        """Main program loop"""
        if not self.initialize():
            return False
        
        if not self.setup_hotkeys():
            return False
        
        try:
            print("\n🎮 Program is running with REAL OFFSETS!")
            print("Use hotkeys to control features. Press F12 to exit.\n")
            
            # Main loop
            while self.running:
                time.sleep(0.1)
        
        except KeyboardInterrupt:
            print("\nReceived keyboard interrupt. Exiting...")
        
        except Exception as e:
            print(f"Unexpected error: {e}")
        
        finally:
            self.cleanup()
        
        return True

def main():
    """Main entry point"""
    print("Starting Sniper Elite 5 with REAL OFFSETS...")
    
    # Check for administrator privileges
    try:
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            print("WARNING: Not running as administrator!")
            print("Some features may not work properly.")
            input("Press Enter to continue anyway, or Ctrl+C to exit...")
    except:
        pass
    
    # Create and run the modification system
    mods = RealOffsetsSniper5Mods()
    success = mods.run()
    
    if success:
        print("Program exited successfully.")
    else:
        print("Program exited with errors.")
    
    input("Press Enter to close...")

if __name__ == "__main__":
    main()
