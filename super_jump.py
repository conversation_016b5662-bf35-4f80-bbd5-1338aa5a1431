import time
import threading
from memory_manager import MemoryManager
from config import GameOffsets

class SuperJump:
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
        self.enabled = False
        self.thread = None
        self.running = False
        
        # Store original values for restoration
        self.original_gravity = None
        self.original_jump_force = None
    
    def toggle(self):
        """Toggle super jump on/off"""
        if self.enabled:
            self.disable()
        else:
            self.enable()
    
    def enable(self):
        """Enable super jump"""
        if not self.memory_manager.process_attached:
            print("Error: Not attached to game process")
            return False
        
        if self.enabled:
            print("Super jump is already enabled")
            return True
        
        # Store original values
        self._store_original_values()
        
        self.enabled = True
        self.running = True
        self.thread = threading.Thread(target=self._super_jump_loop, daemon=True)
        self.thread.start()
        
        print("Super jump enabled! Press F1 to toggle.")
        return True
    
    def disable(self):
        """Disable super jump and restore original values"""
        if not self.enabled:
            print("Super jump is already disabled")
            return
        
        self.enabled = False
        self.running = False
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=1.0)
        
        # Restore original values
        self._restore_original_values()
        
        print("Super jump disabled")
    
    def _store_original_values(self):
        """Store original gravity and jump force values"""
        if not self.memory_manager.player_base:
            return
        
        gravity_addr = self.memory_manager.player_base + GameOffsets.GRAVITY_OFFSET
        jump_force_addr = self.memory_manager.player_base + GameOffsets.JUMP_FORCE_OFFSET
        
        self.original_gravity = self.memory_manager.read_float_safe(gravity_addr)
        self.original_jump_force = self.memory_manager.read_float_safe(jump_force_addr)
    
    def _restore_original_values(self):
        """Restore original gravity and jump force values"""
        if not self.memory_manager.player_base:
            return
        
        gravity_addr = self.memory_manager.player_base + GameOffsets.GRAVITY_OFFSET
        jump_force_addr = self.memory_manager.player_base + GameOffsets.JUMP_FORCE_OFFSET
        
        if self.original_gravity is not None:
            self.memory_manager.write_float_safe(gravity_addr, self.original_gravity)
        
        if self.original_jump_force is not None:
            self.memory_manager.write_float_safe(jump_force_addr, self.original_jump_force)
    
    def _super_jump_loop(self):
        """Main super jump loop"""
        while self.running and self.enabled:
            try:
                # Update base addresses
                if not self.memory_manager.update_base_addresses():
                    time.sleep(GameOffsets.UPDATE_INTERVAL)
                    continue
                
                if not self.memory_manager.player_base:
                    time.sleep(GameOffsets.UPDATE_INTERVAL)
                    continue
                
                gravity_addr = self.memory_manager.player_base + GameOffsets.GRAVITY_OFFSET
                jump_force_addr = self.memory_manager.player_base + GameOffsets.JUMP_FORCE_OFFSET
                
                # Modify gravity (reduce it)
                current_gravity = self.memory_manager.read_float_safe(gravity_addr)
                if current_gravity is not None and current_gravity > 0:
                    modified_gravity = current_gravity * GameOffsets.GRAVITY_MULTIPLIER
                    self.memory_manager.write_float_safe(gravity_addr, modified_gravity)
                
                # Modify jump force (increase it)
                current_jump_force = self.memory_manager.read_float_safe(jump_force_addr)
                if current_jump_force is not None and current_jump_force > 0:
                    modified_jump_force = current_jump_force * GameOffsets.JUMP_FORCE_MULTIPLIER
                    self.memory_manager.write_float_safe(jump_force_addr, modified_jump_force)
                
                time.sleep(GameOffsets.UPDATE_INTERVAL)
                
            except Exception as e:
                print(f"Error in super jump loop: {e}")
                time.sleep(GameOffsets.UPDATE_INTERVAL)
